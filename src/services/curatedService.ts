import { httpBase } from './http/httpBase'

/**
 * Types for Curated Content API
 */
export interface Theme {
  _id: string
  name: string
  name_en: string
  description: string
  description_en: string
  category: string
  icon: string
  color: string
  is_active: boolean
  created_at: string
  updated_at: string
  statistics?: {
    total_content_sets: number
    total_content_items: number
    average_items_per_set: number
  }
}

export interface ContentSet {
  _id: string
  theme_id: string
  title: string
  title_en: string
  description: string
  description_en: string
  difficulty_level: number
  status: 'pending' | 'completed' | 'in_progress' | 'cancelled'
  gentype: 'primary' | 'follow_up' | 'supplementary' | 'review'
  task_item_ids: string[]
  total_items: number
  created_at: string
  theme?: {
    id: string
    name: string
    name_en: string
    icon: string
    color: string
    category: string
  }
}

export interface GeneratedPrompt {
  _id: string
  content: string
  user_id: string
  task_set_id?: string
  created_at: string
  status: 'pending' | 'success' | 'failed'
}

export interface FilterOptions {
  themes: Array<{
    id: string
    name: string
    name_en: string
  }>
  status_values: string[]
  gentype_values: string[]
  difficulty_levels: number[]
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  meta: {
    timestamp: string | null
    request_id: string | null
  }
}

/**
 * Query parameters for API requests
 */
export interface ThemesQuery {
  page?: number
  limit?: number
  search?: string
  category?: string
  is_active?: boolean
}

export interface ContentQuery {
  page?: number
  limit?: number
  theme_id?: string
  difficulty_level?: number
  status?: string
  gentype?: string
  search?: string
}

/**
 * Curated Content Service
 * Handles all API calls for curated content management
 */
export class CuratedService {
  private static readonly BASE_PATH = '/management/curated'

  /**
   * Clean parameters by removing undefined, null, and empty values
   */
  private static cleanParams(params: any): any {
    console.log('🧹 cleanParams - Input:', params)
    const cleaned: any = {}
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          const filteredArray = value.filter(v => v !== undefined && v !== null && v !== '')
          if (filteredArray.length > 0) {
            cleaned[key] = filteredArray
          }
        } else {
          cleaned[key] = value
        }
      }
    })
    console.log('🧹 cleanParams - Output:', cleaned)
    return cleaned
  }

  /**
   * Get all themes with optional filtering
   */
  static async getThemes(params: ThemesQuery = {}): Promise<PaginatedResponse<Theme>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/themes`, { params: cleanedParams })
    return response.data
  }

  /**
   * Get a specific theme by ID with its content sets
   */
  static async getThemeById(
    themeId: string,
    params: Omit<ContentQuery, 'theme_id'> = {}
  ): Promise<PaginatedResponse<ContentSet>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/themes/${themeId}`, { params: cleanedParams })
    return response.data
  }

  /**
   * Get theme details only (without content sets)
   */
  static async getThemeDetails(themeId: string): Promise<ApiResponse<Theme>> {
    const response = await httpBase.get(`${this.BASE_PATH}/theme/${themeId}`)
    return response.data
  }

  /**
   * Get filtered content sets across all themes
   */
  static async getFilteredContent(params: ContentQuery = {}): Promise<PaginatedResponse<ContentSet>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/filtered`, { params: cleanedParams })
    return response.data
  }

  /**
   * Generate new content from prompt
   */
  static async generateContent(content: string): Promise<{
    content: string
    generated_by: string
    status: string
    message: string
  }> {
    const response = await httpBase.post(`${this.BASE_PATH}/generate`, { content })
    return response.data
  }

  /**
   * Get generated prompts history
   */
  static async getPrompts(): Promise<{ data: GeneratedPrompt[] }> {
    const response = await httpBase.post(`${this.BASE_PATH}/get_prompts`)
    return response.data
  }

  /**
   * Get filter options for dropdowns
   */
  static async getFilterOptions(): Promise<ApiResponse<FilterOptions>> {
    const response = await httpBase.get(`${this.BASE_PATH}/filter-options`)
    return response.data
  }
}

/**
 * Export individual methods for easier importing
 */
export const {
  getThemes,
  getThemeById,
  getThemeDetails,
  getFilteredContent,
  generateContent,
  getPrompts,
  getFilterOptions
} = CuratedService
