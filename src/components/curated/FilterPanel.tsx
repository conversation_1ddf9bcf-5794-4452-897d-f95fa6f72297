import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Filter, ChevronDown, X } from 'lucide-react'
import { cn } from '../../utils/cn'

export interface FilterOption {
  value: string
  label: string
  count?: number
}

export interface FilterGroup {
  key: string
  label: string
  options: FilterOption[]
  type: 'select' | 'multiselect' | 'range'
  value?: string | string[] | number[]
}

export interface FilterPanelProps {
  filters: FilterGroup[]
  onFilterChange: (key: string, value: string | string[] | number[]) => void
  onClearAll?: () => void
  className?: string
  isCollapsible?: boolean
  defaultExpanded?: boolean
}

/**
 * FilterPanel Component
 * Reusable filtering interface with multiple filter types
 * Used in Anthology page for content filtering
 */
const FilterPanel: React.FC<FilterPanelProps> = React.memo(({
  filters,
  onFilterChange,
  onClearAll,
  className,
  isCollapsible = true,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(filters.map(f => f.key))
  )

  const toggleGroup = (key: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(key)) {
      newExpanded.delete(key)
    } else {
      newExpanded.add(key)
    }
    setExpandedGroups(newExpanded)
  }

  const hasActiveFilters = filters.some(filter => {
    if (Array.isArray(filter.value)) {
      return filter.value.length > 0
    }
    return filter.value !== undefined && filter.value !== ''
  })

  const getActiveFilterCount = () => {
    return filters.reduce((count, filter) => {
      if (Array.isArray(filter.value)) {
        return count + filter.value.length
      }
      return filter.value !== undefined && filter.value !== '' ? count + 1 : count
    }, 0)
  }

  const renderSelectFilter = (filter: FilterGroup) => (
    <select
      value={filter.value as string || ''}
      onChange={(e) => onFilterChange(filter.key, e.target.value)}
      className={cn(
        'w-full px-3 py-2 rounded-md border border-border',
        'bg-background text-foreground text-sm',
        'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
        'transition-all duration-200'
      )}
    >
      <option value="">All {filter.label}</option>
      {filter.options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label} {option.count && `(${option.count})`}
        </option>
      ))}
    </select>
  )

  const renderMultiSelectFilter = (filter: FilterGroup) => {
    const selectedValues = (filter.value as string[]) || []
    
    return (
      <div className="space-y-2">
        {filter.options.map((option) => (
          <label key={option.value} className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedValues.includes(option.value)}
              onChange={(e) => {
                const newValues = e.target.checked
                  ? [...selectedValues, option.value]
                  : selectedValues.filter(v => v !== option.value)
                onFilterChange(filter.key, newValues)
              }}
              className="rounded border-border text-primary focus:ring-primary/20"
            />
            <span className="text-sm text-foreground">
              {option.label} {option.count && `(${option.count})`}
            </span>
          </label>
        ))}
      </div>
    )
  }

  const renderRangeFilter = (filter: FilterGroup) => {
    const values = (filter.value as number[]) || [1, 3]
    
    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <input
            type="range"
            min="1"
            max="3"
            value={values[0]}
            onChange={(e) => onFilterChange(filter.key, [parseInt(e.target.value), values[1]])}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground w-8">
            {values[0]}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="range"
            min="1"
            max="3"
            value={values[1]}
            onChange={(e) => onFilterChange(filter.key, [values[0], parseInt(e.target.value)])}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground w-8">
            {values[1]}
          </span>
        </div>
        <div className="text-xs text-muted-foreground">
          Difficulty: {values[0]} - {values[1]}
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      'bg-card border border-border rounded-lg',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-primary" />
          <h3 className="font-medium text-foreground">Filters</h3>
          {hasActiveFilters && (
            <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
              {getActiveFilterCount()}
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && onClearAll && (
            <button
              onClick={onClearAll}
              className="text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              Clear all
            </button>
          )}
          {isCollapsible && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-muted rounded transition-colors"
            >
              <ChevronDown className={cn(
                'w-4 h-4 transition-transform duration-200',
                isExpanded ? 'rotate-180' : ''
              )} />
            </button>
          )}
        </div>
      </div>

      {/* Filter Groups */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {filters.map((filter) => (
                <div key={filter.key} className="space-y-2">
                  <button
                    onClick={() => toggleGroup(filter.key)}
                    className="flex items-center justify-between w-full text-left"
                  >
                    <span className="text-sm font-medium text-foreground">
                      {filter.label}
                    </span>
                    <ChevronDown className={cn(
                      'w-3 h-3 transition-transform duration-200',
                      expandedGroups.has(filter.key) ? 'rotate-180' : ''
                    )} />
                  </button>
                  
                  <AnimatePresence>
                    {expandedGroups.has(filter.key) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.15 }}
                        className="overflow-hidden"
                      >
                        <div className="pt-2">
                          {filter.type === 'select' && renderSelectFilter(filter)}
                          {filter.type === 'multiselect' && renderMultiSelectFilter(filter)}
                          {filter.type === 'range' && renderRangeFilter(filter)}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
})

FilterPanel.displayName = 'FilterPanel'

export default FilterPanel
