import React from 'react'
import { motion } from 'framer-motion'
import { Clock, Users, BookOpen, Star } from 'lucide-react'
import { ContentSet } from '../../services/curatedService'
import { cn } from '../../utils/cn'

export interface ContentCardProps {
  content: ContentSet
  onClick?: (content: ContentSet) => void
  className?: string
  showTheme?: boolean
}

/**
 * ContentCard Component
 * Displays a content set with theme information, difficulty, and status
 * Used in Anthology page and theme-specific views
 */
const ContentCard: React.FC<ContentCardProps> = React.memo(({
  content,
  onClick,
  className,
  showTheme = true
}) => {
  const handleClick = () => {
    onClick?.(content)
  }

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-50 border-green-200'
      case 2: return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 3: return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50'
      case 'in_progress': return 'text-blue-600 bg-blue-50'
      case 'pending': return 'text-yellow-600 bg-yellow-50'
      case 'cancelled': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getDifficultyStars = (level: number) => {
    return Array.from({ length: 3 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'w-3 h-3',
          i < level ? 'fill-current text-yellow-400' : 'text-gray-300'
        )}
      />
    ))
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'bg-card border border-border rounded-lg p-4 cursor-pointer',
        'hover:shadow-lg hover:border-primary/20 transition-all duration-200',
        'group relative overflow-hidden',
        className
      )}
      onClick={handleClick}
    >
      {/* Theme indicator stripe */}
      {showTheme && content.theme && (
        <div 
          className="absolute top-0 left-0 w-full h-1 rounded-t-lg"
          style={{ backgroundColor: content.theme.color }}
        />
      )}

      {/* Header with theme info */}
      {showTheme && content.theme && (
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">{content.theme.icon}</span>
          <span className="text-sm text-muted-foreground font-medium">
            {content.theme.name_en}
          </span>
        </div>
      )}

      {/* Content title and description */}
      <div className="space-y-2 mb-4">
        <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
          {content.title_en || content.title}
        </h3>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {content.description_en || content.description}
        </p>
      </div>

      {/* Metadata */}
      <div className="space-y-3">
        {/* Difficulty and items count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            {getDifficultyStars(content.difficulty_level)}
            <span className="text-xs text-muted-foreground ml-1">
              Level {content.difficulty_level}
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <BookOpen className="w-3 h-3" />
            <span>{content.total_items} items</span>
          </div>
        </div>

        {/* Status and type */}
        <div className="flex items-center justify-between">
          <span className={cn(
            'px-2 py-1 rounded-full text-xs font-medium',
            getStatusColor(content.status)
          )}>
            {content.status.replace('_', ' ')}
          </span>
          <span className="text-xs text-muted-foreground capitalize">
            {content.gentype}
          </span>
        </div>

        {/* Created date */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="w-3 h-3" />
          <span>{new Date(content.created_at).toLocaleDateString()}</span>
        </div>
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
    </motion.div>
  )
})

ContentCard.displayName = 'ContentCard'

export default ContentCard
