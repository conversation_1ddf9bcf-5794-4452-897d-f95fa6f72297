import React, { useState, useCallback, useEffect } from 'react'
import { Search, X } from 'lucide-react'
import { cn } from '../../utils/cn'

export interface SearchBarProps {
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  onSearch?: (value: string) => void
  className?: string
  debounceMs?: number
  autoFocus?: boolean
}

/**
 * SearchBar Component
 * Reusable search input with debouncing and clear functionality
 * Used across Anthology and Atelier pages
 */
const SearchBar: React.FC<SearchBarProps> = React.memo(({
  placeholder = 'Search...',
  value = '',
  onChange,
  onSearch,
  className,
  debounceMs = 300,
  autoFocus = false
}) => {
  const [localValue, setLocalValue] = useState(value)
  const [isFocused, setIsFocused] = useState(false)

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localValue !== value) {
        onChange?.(localValue)
        onSearch?.(localValue)
      }
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [localValue, onChange, onSearch, debounceMs, value])

  // Sync with external value changes
  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value)
    }
  }, [value])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value)
  }, [])

  const handleClear = useCallback(() => {
    setLocalValue('')
    onChange?.('')
    onSearch?.('')
  }, [onChange, onSearch])

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      onSearch?.(localValue)
    }
    if (e.key === 'Escape') {
      handleClear()
    }
  }, [localValue, onSearch, handleClear])

  return (
    <div className={cn(
      'relative flex items-center',
      className
    )}>
      {/* Search icon */}
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
        <Search className={cn(
          'w-4 h-4 transition-colors duration-200',
          isFocused ? 'text-primary' : 'text-muted-foreground'
        )} />
      </div>

      {/* Input field */}
      <input
        type="text"
        value={localValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        autoFocus={autoFocus}
        className={cn(
          'w-full pl-10 pr-10 py-2.5 rounded-lg border border-border',
          'bg-background text-foreground placeholder:text-muted-foreground',
          'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
          'transition-all duration-200',
          'hover:border-primary/50',
          isFocused && 'ring-2 ring-primary/20 border-primary'
        )}
      />

      {/* Clear button */}
      {localValue && (
        <button
          onClick={handleClear}
          className={cn(
            'absolute right-3 top-1/2 transform -translate-y-1/2 z-10',
            'p-1 rounded-full hover:bg-muted transition-colors duration-200',
            'text-muted-foreground hover:text-foreground'
          )}
          type="button"
          aria-label="Clear search"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </div>
  )
})

SearchBar.displayName = 'SearchBar'

export default SearchBar
