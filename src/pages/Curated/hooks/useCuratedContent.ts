import { useState, useEffect, useCallback } from 'react'
import { 
  CuratedService, 
  ContentSet, 
  ContentQuery, 
  PaginatedResponse 
} from '../../../services/curatedService'

export interface UseCuratedContentReturn {
  content: ContentSet[]
  loading: boolean
  error: string | null
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
  }
  filters: ContentQuery
  setFilters: (filters: Partial<ContentQuery>) => void
  refetch: () => Promise<void>
  clearFilters: () => void
}

/**
 * Custom hook for managing curated content with filtering and pagination
 * Used in Anthology page and theme-specific content views
 */
const useCuratedContent = (initialFilters: ContentQuery = {}): UseCuratedContentReturn => {
  const [content, setContent] = useState<ContentSet[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  })
  const [filters, setFiltersState] = useState<ContentQuery>({
    page: 1,
    limit: 20,
    ...initialFilters
  })

  const fetchContent = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response: PaginatedResponse<ContentSet> = await CuratedService.getFilteredContent(filters)
      
      setContent(response.data)
      setPagination({
        currentPage: response.meta.page,
        totalPages: response.meta.total_pages,
        totalItems: response.meta.total,
        itemsPerPage: response.meta.limit
      })
    } catch (err) {
      console.error('Error fetching curated content:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch content')
      setContent([])
    } finally {
      setLoading(false)
    }
  }, [filters])

  const setFilters = useCallback((newFilters: Partial<ContentQuery>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except when explicitly setting page)
      page: newFilters.page !== undefined ? newFilters.page : 1
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFiltersState({
      page: 1,
      limit: filters.limit || 20
    })
  }, [filters.limit])

  const refetch = useCallback(async () => {
    await fetchContent()
  }, [fetchContent])

  // Fetch content when filters change
  useEffect(() => {
    fetchContent()
  }, [fetchContent])

  return {
    content,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refetch,
    clearFilters
  }
}

export default useCuratedContent
