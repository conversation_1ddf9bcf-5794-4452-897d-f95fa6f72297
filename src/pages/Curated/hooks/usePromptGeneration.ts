import { useState, useCallback } from 'react'
import { 
  CuratedService, 
  GeneratedPrompt 
} from '../../../services/curatedService'

export interface UsePromptGenerationReturn {
  generating: boolean
  error: string | null
  generateContent: (prompt: string) => Promise<boolean>
  clearError: () => void
}

/**
 * Custom hook for managing prompt generation
 * Used in Playground page for content generation
 */
const usePromptGeneration = (): UsePromptGenerationReturn => {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generateContent = useCallback(async (prompt: string): Promise<boolean> => {
    if (!prompt.trim()) {
      setError('Please enter a prompt')
      return false
    }

    try {
      setGenerating(true)
      setError(null)

      const response = await CuratedService.generateContent(prompt.trim())
      
      if (response.status === 'success') {
        return true
      } else {
        setError(response.message || 'Generation failed')
        return false
      }
    } catch (err) {
      console.error('Error generating content:', err)
      setError(err instanceof Error ? err.message : 'Failed to generate content')
      return false
    } finally {
      setGenerating(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    generating,
    error,
    generateContent,
    clearError
  }
}

export default usePromptGeneration
