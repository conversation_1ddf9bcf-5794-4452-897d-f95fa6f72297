import { useState, useEffect, useCallback } from 'react'
import { 
  CuratedService, 
  Theme, 
  ThemesQuery, 
  PaginatedResponse 
} from '../../../services/curatedService'

export interface UseThemesReturn {
  themes: Theme[]
  loading: boolean
  error: string | null
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
  }
  filters: ThemesQuery
  setFilters: (filters: Partial<ThemesQuery>) => void
  refetch: () => Promise<void>
  clearFilters: () => void
  getThemeById: (id: string) => Theme | undefined
}

/**
 * Custom hook for managing themes with filtering and pagination
 * Used in Atelier page and theme selection components
 */
const useThemes = (initialFilters: ThemesQuery = {}): UseThemesReturn => {
  const [themes, setThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  })
  const [filters, setFiltersState] = useState<ThemesQuery>({
    page: 1,
    limit: 10,
    is_active: true,
    ...initialFilters
  })

  const fetchThemes = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response: PaginatedResponse<Theme> = await CuratedService.getThemes(filters)
      
      setThemes(response.data)
      setPagination({
        currentPage: response.meta.page,
        totalPages: response.meta.total_pages,
        totalItems: response.meta.total,
        itemsPerPage: response.meta.limit
      })
    } catch (err) {
      console.error('Error fetching themes:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch themes')
      setThemes([])
    } finally {
      setLoading(false)
    }
  }, [filters])

  const setFilters = useCallback((newFilters: Partial<ThemesQuery>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except when explicitly setting page)
      page: newFilters.page !== undefined ? newFilters.page : 1
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFiltersState({
      page: 1,
      limit: filters.limit || 10,
      is_active: true
    })
  }, [filters.limit])

  const refetch = useCallback(async () => {
    await fetchThemes()
  }, [fetchThemes])

  const getThemeById = useCallback((id: string) => {
    return themes.find(theme => theme._id === id)
  }, [themes])

  // Fetch themes when filters change
  useEffect(() => {
    fetchThemes()
  }, [fetchThemes])

  return {
    themes,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refetch,
    clearFilters,
    getThemeById
  }
}

export default useThemes
