import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Clock, CheckCircle, AlertCircle, Loader2, RefreshCw } from 'lucide-react'
import { CuratedService, GeneratedPrompt } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

export interface PromptHistoryProps {
  refreshTrigger?: number
  className?: string
}

/**
 * PromptHistory Component
 * Displays history of generated prompts with status indicators
 * Used in Playground page
 */
const PromptHistory: React.FC<PromptHistoryProps> = React.memo(({
  refreshTrigger,
  className
}) => {
  const [prompts, setPrompts] = useState<GeneratedPrompt[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPrompts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await CuratedService.getPrompts()
      setPrompts(response.data || [])
    } catch (err) {
      console.error('Error fetching prompts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch prompts')
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch prompts on mount and when refresh trigger changes
  useEffect(() => {
    fetchPrompts()
  }, [fetchPrompts, refreshTrigger])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'pending':
        return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className={cn('bg-card border border-border rounded-xl p-6', className)}>
        <div className="flex items-center gap-3 mb-6">
          <Clock className="w-5 h-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Generation History</h3>
        </div>
        
        <div className="space-y-4">
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-start gap-3 p-4 border border-border rounded-lg">
                <div className="w-4 h-4 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
                <div className="h-6 bg-muted rounded w-16" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('bg-card border border-border rounded-xl p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Clock className="w-5 h-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Generation History</h3>
          {prompts.length > 0 && (
            <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
              {prompts.length}
            </span>
          )}
        </div>
        
        <button
          onClick={fetchPrompts}
          disabled={loading}
          className={cn(
            'p-2 rounded-lg border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <RefreshCw className={cn('w-4 h-4', loading && 'animate-spin')} />
        </button>
      </div>

      {/* Error state */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </div>
      )}

      {/* Prompts list */}
      {prompts.length === 0 ? (
        <div className="text-center py-8">
          <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">No generation history yet</p>
          <p className="text-sm text-muted-foreground mt-1">
            Generated prompts will appear here
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          <AnimatePresence>
            {prompts.map((prompt, index) => (
              <motion.div
                key={prompt._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="flex items-start gap-3 p-4 border border-border rounded-lg hover:border-primary/30 transition-colors duration-200"
              >
                {/* Status icon */}
                <div className="flex-shrink-0 mt-1">
                  {getStatusIcon(prompt.status)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground line-clamp-2 mb-2">
                    {prompt.content}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{formatDate(prompt.created_at)}</span>
                    {prompt.task_set_id && (
                      <>
                        <span>•</span>
                        <span>Task Set: {prompt.task_set_id.slice(-8)}</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Status badge */}
                <div className={cn(
                  'px-2 py-1 rounded-full text-xs font-medium border',
                  getStatusColor(prompt.status)
                )}>
                  {prompt.status}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </div>
  )
})

PromptHistory.displayName = 'PromptHistory'

export default PromptHistory
