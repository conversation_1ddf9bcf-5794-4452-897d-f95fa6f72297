import React from 'react'
import { RefreshCw } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { 
  ContentCard, 
  SearchBar, 
  PaginationControls, 
  LoadingGrid, 
  EmptyState 
} from '../../../components/curated'
import { ContentSet, FilterOptions } from '../../../services/curatedService'
import { ContentSetFilter } from '../pages/curated.anthology'
import { cn } from '../../../utils/cn'

interface AnthologyComponentProps {
  loading: boolean
  content: ContentSet[]
  error: string | null
  filter: ContentSetFilter
  filterOptions: FilterOptions | null
  loadingFilters: boolean
  totalItems: number
  totalPages: number
  onFilterChange: (filter: ContentSetFilter) => void
  onPageChange: (page: number) => void
  onPageSizeChange: (limit: number) => void
  onContentClick: (content: ContentSet) => void
  onRefresh: () => void
}

/**
 * Anthology Component - Pure UI component for curated content list
 */
const AnthologyComponent: React.FC<AnthologyComponentProps> = ({
  loading,
  content,
  error,
  filter,
  filterOptions,
  loadingFilters,
  totalItems,
  totalPages,
  onFilterChange,
  onPageChange,
  onPageSizeChange,
  onContentClick,
  onRefresh
}) => {
  // Check if there are active filters
  const hasActiveFilters = Object.entries(filter).some(([key, value]) => {
    if (key === 'page' || key === 'limit') return false
    return value !== undefined && value !== ''
  })

  // Handle individual filter changes
  const handleFilterFieldChange = (key: string, value: string) => {
    const newFilter: any = { ...filter, page: 1 }

    // Only set the key if value is not empty, otherwise remove it completely
    if (value && value.trim() !== '') {
      if (key === 'difficulty_level') {
        newFilter[key] = parseInt(value)
      } else {
        newFilter[key] = value
      }
    } else {
      // Remove the key completely from the filter object
      delete newFilter[key]
    }

    onFilterChange(newFilter)
  }

  const handleSearch = (searchTerm: string) => {
    const newFilter: any = { ...filter, page: 1 }

    // Only set search if it has a value, otherwise remove it
    if (searchTerm && searchTerm.trim() !== '') {
      newFilter.search = searchTerm.trim()
    } else {
      delete newFilter.search
    }

    onFilterChange(newFilter)
  }

  const handleClearFilters = () => {
    onFilterChange({
      page: 1,
      limit: filter.limit
    })
  }

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-4">
      {/* Search bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search content sets..."
            value={filter.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>
        
        {/* Active filters indicator and clear button */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {Object.entries(filter).filter(([key, value]) => {
                if (key === 'page' || key === 'limit') return false
                return value !== undefined && value !== ''
              }).length} filters active
            </span>
            <button
              onClick={handleClearFilters}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Clear all
            </button>
          </div>
        )}

        {/* Refresh button */}
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            'p-2 rounded-lg border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <RefreshCw className={cn('w-4 h-4', loading && 'animate-spin')} />
        </button>
      </div>

      {/* Always visible filter options */}
      {!loadingFilters && filterOptions && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Theme filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Theme</label>
            <select
              value={filter.theme_id || ''}
              onChange={(e) => handleFilterFieldChange('theme_id', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Themes</option>
              {filterOptions.themes.map((theme) => (
                <option key={theme.id} value={theme.id}>
                  {theme.name_en}
                </option>
              ))}
            </select>
          </div>

          {/* Status filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Status</label>
            <select
              value={filter.status || ''}
              onChange={(e) => handleFilterFieldChange('status', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Status</option>
              {filterOptions.status_values.map((status) => (
                <option key={status} value={status}>
                  {status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Generation Type filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Type</label>
            <select
              value={filter.gentype || ''}
              onChange={(e) => handleFilterFieldChange('gentype', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Types</option>
              {filterOptions.gentype_values.map((gentype) => (
                <option key={gentype} value={gentype}>
                  {gentype.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Difficulty</label>
            <select
              value={filter.difficulty_level || ''}
              onChange={(e) => handleFilterFieldChange('difficulty_level', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Levels</option>
              <option value="1">Level 1 (Easy)</option>
              <option value="2">Level 2 (Medium)</option>
              <option value="3">Level 3 (Hard)</option>
            </select>
          </div>
        </div>
      )}
    </div>
  )

  // Bottom content (pagination)
  const bottomContent = !loading && !error && content.length > 0 ? (
    <PaginationControls
      currentPage={filter.page}
      totalPages={totalPages}
      totalItems={totalItems}
      itemsPerPage={filter.limit}
      onPageChange={onPageChange}
      onItemsPerPageChange={onPageSizeChange}
    />
  ) : null

  return (
    <MainLayout
      title="📚 Anthology"
      description="Discover and explore curated content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Content Area */}
      {loading ? (
        <LoadingGrid count={8} type="content" />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load content"
          description={error}
          actionLabel="Try again"
          onAction={onRefresh}
        />
      ) : content.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'content'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : onRefresh}
        />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {content.map((item) => (
            <ContentCard
              key={item._id}
              content={item}
              onClick={onContentClick}
              showTheme={true}
            />
          ))}
        </div>
      )}
    </MainLayout>
  )
}

export default AnthologyComponent
