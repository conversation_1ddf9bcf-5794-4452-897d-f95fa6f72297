import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>ap, Loader2, AlertCircle, CheckCircle, Trash2 } from 'lucide-react'
import { usePromptGeneration } from '../hooks'
import { cn } from '../../../utils/cn'

export interface PromptEditorProps {
  onGenerate?: (prompt: string) => void
  className?: string
}

/**
 * PromptEditor Component
 * Admin interface for content generation with prompt input and controls
 * Used in Playground page
 */
const PromptEditor: React.FC<PromptEditorProps> = React.memo(({
  onGenerate,
  className
}) => {
  const [prompt, setPrompt] = useState('')
  const [lastGeneratedPrompt, setLastGeneratedPrompt] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)

  const {
    generating,
    error,
    generateContent,
    clearError
  } = usePromptGeneration()

  const handleGenerate = useCallback(async () => {
    if (!prompt.trim()) return

    const success = await generateContent(prompt)
    
    if (success) {
      setLastGeneratedPrompt(prompt)
      setShowSuccess(true)
      onGenerate?.(prompt)
      
      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000)
    }
  }, [prompt, generateContent, onGenerate])

  const handleClear = useCallback(() => {
    setPrompt('')
    clearError()
  }, [clearError])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleGenerate()
    }
  }, [handleGenerate])

  const promptLength = prompt.length
  const maxLength = 2000
  const isNearLimit = promptLength > maxLength * 0.8

  return (
    <div className={cn(
      'bg-card border border-border rounded-xl p-6 space-y-6',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-cyan-500 rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Content Generator</h2>
            <p className="text-sm text-muted-foreground">
              Create new content sets with AI assistance
            </p>
          </div>
        </div>
        
        {/* Success indicator */}
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm font-medium">Generated successfully!</span>
          </motion.div>
        )}
      </div>

      {/* Prompt input */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label htmlFor="prompt" className="text-sm font-medium text-foreground">
            Content Prompt
          </label>
          <div className={cn(
            'text-xs transition-colors duration-200',
            isNearLimit ? 'text-red-500' : 'text-muted-foreground'
          )}>
            {promptLength}/{maxLength}
          </div>
        </div>
        
        <textarea
          id="prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Describe the content you want to generate... 

Example: Generate questions about Nepali festivals including Dashain, Tihar, and Holi. Include questions about traditions, food, and cultural significance. Make them suitable for intermediate level learners."
          maxLength={maxLength}
          className={cn(
            'w-full h-32 px-4 py-3 rounded-lg border border-border',
            'bg-background text-foreground placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
            'transition-all duration-200 resize-none',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
          )}
        />
        
        {/* Helper text */}
        <p className="text-xs text-muted-foreground">
          💡 Tip: Use Ctrl+Enter (Cmd+Enter on Mac) to generate quickly
        </p>
      </div>

      {/* Error display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </motion.div>
      )}

      {/* Last generated prompt */}
      {lastGeneratedPrompt && lastGeneratedPrompt !== prompt && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground mb-1">Last generated:</p>
          <p className="text-sm text-foreground line-clamp-2">{lastGeneratedPrompt}</p>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex items-center gap-3">
        <button
          onClick={handleGenerate}
          disabled={!prompt.trim() || generating}
          className={cn(
            'flex items-center gap-2 px-6 py-3 rounded-lg font-medium',
            'bg-gradient-to-r from-green-500 to-cyan-500 text-white',
            'hover:from-green-600 hover:to-cyan-600 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-green-500/20',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'transform hover:scale-105 active:scale-95'
          )}
        >
          {generating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <Zap className="w-4 h-4" />
              <span>Generate Content</span>
            </>
          )}
        </button>

        <button
          onClick={handleClear}
          disabled={!prompt && !error}
          className={cn(
            'flex items-center gap-2 px-4 py-3 rounded-lg',
            'border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <Trash2 className="w-4 h-4" />
          <span>Clear</span>
        </button>
      </div>
    </div>
  )
})

PromptEditor.displayName = 'PromptEditor'

export default PromptEditor
