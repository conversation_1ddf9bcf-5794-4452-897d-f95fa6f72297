import React, { useState, useCallback } from 'react'
import { Zap, Shield, AlertTriangle } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { PromptEditor, PromptHistory } from '../components'
import { useAppSelector } from '../../../store/hooks'
import { cn } from '../../../utils/cn'

/**
 * Playground Page
 * Admin-only content generation interface
 * Provides prompt editor and generation history
 */
const Playground: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const user = useAppSelector(state => state.auth.user)

  // Check if user has admin privileges
  // Note: This should be replaced with proper role checking from your auth system
  const isAdmin = user?.role === 'admin' || user?.role === 'agent'

  const handleGenerate = useCallback((prompt: string) => {
    // Trigger refresh of prompt history
    setRefreshTrigger(prev => prev + 1)
  }, [])

  // Admin access check
  if (!isAdmin) {
    return (
      <MainLayout
        title="⚡ Playground"
        description="Content generation workspace"
        className="space-y-6"
      >
        <div className="flex flex-col items-center justify-center py-16 px-6 text-center">
          <div className="w-16 h-16 bg-red-50 dark:bg-red-950/20 rounded-full flex items-center justify-center mb-6">
            <Shield className="w-8 h-8 text-red-500" />
          </div>
          
          <h2 className="text-xl font-semibold text-foreground mb-2">
            Access Restricted
          </h2>
          
          <p className="text-muted-foreground max-w-md mb-6">
            This area is restricted to administrators only. You need admin privileges to access the content generation tools.
          </p>
          
          <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm text-yellow-700 dark:text-yellow-300">
              Contact your administrator for access
            </span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout
      title="⚡ Playground"
      description="Generate and manage curated content with AI assistance"
      className="space-y-6"
    >
      {/* Admin indicator */}
      <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
        <Shield className="w-4 h-4 text-green-600" />
        <span className="text-sm text-green-700 dark:text-green-300 font-medium">
          Admin Access Granted
        </span>
        <span className="text-xs text-green-600 dark:text-green-400">
          • Content Generation • Prompt Management
        </span>
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Prompt Editor */}
        <div className="space-y-6">
          <PromptEditor 
            onGenerate={handleGenerate}
            className="h-fit"
          />
          
          {/* Quick tips */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h4 className="font-medium text-foreground flex items-center gap-2">
              <Zap className="w-4 h-4 text-primary" />
              Generation Tips
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Be specific about the topic and difficulty level</li>
              <li>• Include context about the target audience</li>
              <li>• Mention specific cultural elements when relevant</li>
              <li>• Specify the type of questions or content needed</li>
            </ul>
          </div>
        </div>

        {/* Prompt History */}
        <div className="space-y-6">
          <PromptHistory 
            refreshTrigger={refreshTrigger}
            className="h-fit"
          />
          
          {/* Generation stats */}
          <div className="bg-card border border-border rounded-lg p-4">
            <h4 className="font-medium text-foreground mb-3">Quick Stats</h4>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-primary">
                  {refreshTrigger}
                </div>
                <div className="text-xs text-muted-foreground">
                  Generated Today
                </div>
              </div>
              <div>
                <div className="text-lg font-bold text-primary">
                  {user?.name?.split(' ')[0] || 'Admin'}
                </div>
                <div className="text-xs text-muted-foreground">
                  Current User
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer info */}
      <div className="mt-8 p-4 bg-muted/30 rounded-lg border border-dashed border-border">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            💡 Generated content will be processed and added to the content library
          </span>
          <span>
            Last updated: {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>
    </MainLayout>
  )
}

export default Playground
