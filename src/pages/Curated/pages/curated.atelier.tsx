import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Palette, Grid, List } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { 
  ThemeCard, 
  SearchBar, 
  LoadingGrid, 
  EmptyState 
} from '../../../components/curated'
import { useThemes } from '../hooks'
import { Theme } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

/**
 * Atelier Page
 * Theme discovery and selection gallery
 * Displays all available themes with statistics and filtering
 */
const Atelier: React.FC = () => {
  const navigate = useNavigate()
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  const {
    themes,
    loading,
    error,
    filters,
    setFilters,
    refetch,
    clearFilters
  } = useThemes()

  // Get unique categories from themes
  const categories = React.useMemo(() => {
    const cats = themes.map(theme => theme.category)
    return Array.from(new Set(cats)).sort()
  }, [themes])

  const handleSearch = useCallback((searchTerm: string) => {
    setFilters({ search: searchTerm, page: 1 })
  }, [setFilters])

  const handleCategoryFilter = useCallback((category: string) => {
    setSelectedCategory(category)
    setFilters({ category: category || undefined, page: 1 })
  }, [setFilters])

  const handleThemeClick = useCallback((theme: Theme) => {
    // Navigate to Anthology page filtered by this theme
    console.log('🎨 Atelier - Clicking theme:', theme._id, theme.name_en)
    const url = `/anthology?theme_id=${theme._id}`
    console.log('🎨 Atelier - Navigating to:', url)
    navigate(url)
  }, [navigate])

  const handleClearFilters = useCallback(() => {
    setSelectedCategory('')
    clearFilters()
  }, [clearFilters])

  const hasActiveFilters = !!(filters.search || filters.category)

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-4">
      {/* Search and view controls */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search themes..."
            value={filters.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-4">
          {/* Active filters indicator and clear button */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {[filters.search, filters.category].filter(Boolean).length} filters active
              </span>
              <button
                onClick={handleClearFilters}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
              >
                Clear all
              </button>
            </div>
          )}

          {/* View mode toggle */}
          <div className="flex items-center border border-border rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'grid'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'list'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Always visible filter options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Category filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">Category</label>
          <select
            value={selectedCategory}
            onChange={(e) => handleCategoryFilter(e.target.value)}
            className={cn(
              'w-full px-3 py-2 rounded-md border border-border',
              'bg-background text-foreground text-sm',
              'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
              'transition-all duration-200'
            )}
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* Active status filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">Status</label>
          <select
            value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
            onChange={(e) => {
              const value = e.target.value
              if (value === 'all') {
                setFilters({ is_active: undefined })
              } else {
                setFilters({ is_active: value === 'active' })
              }
            }}
            className={cn(
              'w-full px-3 py-2 rounded-md border border-border',
              'bg-background text-foreground text-sm',
              'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
              'transition-all duration-200'
            )}
          >
            <option value="all">All Themes</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>

        {/* Sort by */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">Sort By</label>
          <select
            className={cn(
              'w-full px-3 py-2 rounded-md border border-border',
              'bg-background text-foreground text-sm',
              'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
              'transition-all duration-200'
            )}
          >
            <option value="name">Name (A-Z)</option>
            <option value="category">Category</option>
            <option value="content_count">Content Count</option>
            <option value="created_date">Created Date</option>
          </select>
        </div>
      </div>
    </div>
  )

  // Bottom content (stats summary)
  const bottomContent = !loading && themes.length > 0 ? (
    <div className="p-4 bg-muted/50 rounded-lg">
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-primary">{themes.length}</div>
          <div className="text-sm text-muted-foreground">Total Themes</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">{categories.length}</div>
          <div className="text-sm text-muted-foreground">Categories</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">
            {themes.reduce((sum, theme) => sum + (theme.statistics?.total_content_sets || 0), 0)}
          </div>
          <div className="text-sm text-muted-foreground">Content Sets</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">
            {themes.reduce((sum, theme) => sum + (theme.statistics?.total_content_items || 0), 0)}
          </div>
          <div className="text-sm text-muted-foreground">Total Items</div>
        </div>
      </div>
    </div>
  ) : null

  return (
    <MainLayout
      title="🎨 Atelier"
      description="Explore themes and discover content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Themes grid/list */}
      {loading ? (
        <LoadingGrid
          count={8}
          type="theme"
          className={cn(
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          )}
        />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load themes"
          description={error}
          actionLabel="Try again"
          onAction={refetch}
        />
      ) : themes.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'themes'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : refetch}
        />
      ) : (
        <div className={cn(
          'grid gap-6',
          viewMode === 'grid'
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1 max-w-4xl mx-auto'
        )}>
          {themes.map((theme) => (
            <ThemeCard
              key={theme._id}
              theme={theme}
              onClick={handleThemeClick}
              showStats={true}
              className={cn(
                viewMode === 'list' && 'flex-row items-center p-4'
              )}
            />
          ))}
        </div>
      )}
    </MainLayout>
  )
}

export default Atelier
