import React, { useEffect, useState, useRef, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAppSelector } from '../../../store/hooks'
import { CuratedService, ContentSet, FilterOptions } from '../../../services/curatedService'
import AnthologyComponent from '../components/AnthologyComponent'

// Filter interface for content sets
export interface ContentSetFilter {
  page: number
  limit: number
  theme_id?: string
  difficulty_level?: number
  status?: string
  gentype?: string
  search?: string
}

// Export ContentSet type for component
export type { ContentSet }

/**
 * Anthology Container - Handles logic and state for curated content list
 */
const AnthologyContainer: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [content, setContent] = useState<ContentSet[]>([])
  const [error, setError] = useState<string | null>(null)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null)
  const [loadingFilters, setLoadingFilters] = useState(true)

  // Refs to prevent duplicate API calls
  const filterOptionsLoadedRef = useRef(false)
  const contentLoadingRef = useRef(false)

  // Get initial filter from URL params
  const getInitialFilter = (): ContentSetFilter => {
    const filter: ContentSetFilter = {
      page: 1,
      limit: 20
    }

    const themeId = searchParams.get('theme_id')
    const difficultyLevel = searchParams.get('difficulty_level')
    const status = searchParams.get('status')
    const gentype = searchParams.get('gentype')
    const search = searchParams.get('search')
    const page = searchParams.get('page')

    console.log('🔗 RAW URL PARAMS:')
    console.log('  theme_id:', themeId)
    console.log('  difficulty_level:', difficultyLevel)
    console.log('  status:', status)
    console.log('  gentype:', gentype)
    console.log('  search:', search)
    console.log('  page:', page)

    if (themeId) filter.theme_id = themeId
    if (difficultyLevel) filter.difficulty_level = parseInt(difficultyLevel)
    if (status) filter.status = status
    if (gentype) filter.gentype = gentype
    if (search) filter.search = search
    if (page) filter.page = parseInt(page)

    console.log('🔗 INITIAL FILTER CREATED:', filter)
    return filter
  }

  // Filter state
  const [filter, setFilter] = useState<ContentSetFilter>(getInitialFilter())

  // Update filter when URL changes
  useEffect(() => {
    const newFilter = getInitialFilter()
    setFilter(newFilter)
  }, [searchParams])

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Cleanup refs on unmount
  useEffect(() => {
    return () => {
      filterOptionsLoadedRef.current = false
      contentLoadingRef.current = false
    }
  }, [])

  // Fetch filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      if (filterOptionsLoadedRef.current) return

      try {
        filterOptionsLoadedRef.current = true
        setLoadingFilters(true)
        const response = await CuratedService.getFilterOptions()
        setFilterOptions(response.data)
      } catch (err) {
        console.error('Failed to fetch filter options:', err)
        filterOptionsLoadedRef.current = false
      } finally {
        setLoadingFilters(false)
      }
    }

    if (isAuthenticated && user && !filterOptionsLoadedRef.current) {
      fetchFilterOptions()
    }
  }, [isAuthenticated, user])

  // Fetch content
  useEffect(() => {
    const fetchContent = async () => {
      if (!user) return
      if (contentLoadingRef.current) return

      try {
        contentLoadingRef.current = true
        setLoading(true)
        setError(null)

        console.log('🔍 Final filter being sent to API:', filter)
        const response = await CuratedService.getFilteredContent(filter)

        setContent(response.data)
        setTotalItems(response.meta.total)
        setTotalPages(response.meta.total_pages)
      } catch (err) {
        console.error('Error fetching content:', err)
        setError('Failed to load content. Please try again.')
      } finally {
        setLoading(false)
        contentLoadingRef.current = false
      }
    }

    if (isAuthenticated && user) {
      fetchContent()
    }
  }, [isAuthenticated, user, filter])

  // Update URL when filter changes
  const updateURL = useCallback((newFilter: ContentSetFilter) => {
    const params = new URLSearchParams()
    Object.entries(newFilter).forEach(([key, value]) => {
      if (value && value !== '' && value !== undefined && value !== null) {
        params.set(key, value.toString())
      }
    })
    setSearchParams(params)
  }, [setSearchParams])

  // Handle filter changes
  const handleFilterChange = (newFilter: ContentSetFilter) => {
    console.log('🎛️ FILTER CHANGE - New filter:', newFilter)
    setFilter(newFilter)
    updateURL(newFilter)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    const newFilter = { ...filter, page }
    handleFilterChange(newFilter)
  }

  // Handle page size change
  const handlePageSizeChange = (limit: number) => {
    const newFilter = { ...filter, limit, page: 1 }
    handleFilterChange(newFilter)
  }

  // Navigate to content details
  const handleContentClick = (content: ContentSet) => {
    navigate(`/tasks/${content._id}`)
  }

  // Handle refresh
  const handleRefresh = () => {
    filterOptionsLoadedRef.current = false
    contentLoadingRef.current = false
    setFilter(prev => ({ ...prev, _timestamp: Date.now() } as ContentSetFilter))
  }

  return (
    <AnthologyComponent
      loading={loading}
      content={content}
      error={error}
      filter={filter}
      filterOptions={filterOptions}
      loadingFilters={loadingFilters}
      totalItems={totalItems}
      totalPages={totalPages}
      onFilterChange={handleFilterChange}
      onPageChange={handlePageChange}
      onPageSizeChange={handlePageSizeChange}
      onContentClick={handleContentClick}
      onRefresh={handleRefresh}
    />
  )
}

export default AnthologyContainer
