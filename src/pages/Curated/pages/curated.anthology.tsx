import React, { useCallback, useEffect, useMemo } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import MainLayout from '../../../components/layout/MainLayout'
import {
  ContentCard,
  SearchBar,
  PaginationControls,
  LoadingGrid,
  EmptyState
} from '../../../components/curated'
import { useCuratedContent, useContentFilters } from '../hooks'
import { ContentSet } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

/**
 * Anthology Page
 * Main curated content gallery with filtering and search
 * Displays all content sets with theme information
 */
const Anthology: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [urlProcessed, setUrlProcessed] = useState(false)

  // Get initial filters from URL params
  const getInitialFilters = () => {
    const urlFilters: any = {}
    const themeId = searchParams.get('theme_id')
    const difficultyLevel = searchParams.get('difficulty_level')
    const status = searchParams.get('status')
    const gentype = searchParams.get('gentype')
    const search = searchParams.get('search')
    const page = searchParams.get('page')

    console.log('🔗 Anthology - All URL params:', Object.fromEntries(searchParams.entries()))
    console.log('🔗 Anthology - theme_id from URL:', themeId)

    if (themeId) urlFilters.theme_id = themeId
    if (difficultyLevel) urlFilters.difficulty_level = parseInt(difficultyLevel)
    if (status) urlFilters.status = status
    if (gentype) urlFilters.gentype = gentype
    if (search) urlFilters.search = search
    if (page) urlFilters.page = parseInt(page)

    console.log('🔗 Anthology - Initial filters:', urlFilters)
    return urlFilters
  }

  const {
    content,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refetch,
    clearFilters
  } = useCuratedContent(getInitialFilters())

  // Mark URL as processed after component mounts
  useEffect(() => {
    setUrlProcessed(true)
  }, [])

  const {
    filterGroups,
    loading: filtersLoading
  } = useContentFilters()

  // Update URL when filters change
  const updateURL = useCallback((newFilters: any) => {
    const params = new URLSearchParams()
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '' && value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => v && params.append(key, v))
        } else {
          params.set(key, value.toString())
        }
      }
    })
    setSearchParams(params)
  }, [setSearchParams])

  const handleFilterChange = useCallback((key: string, value: string | string[] | number[]) => {
    // Clean the value - remove empty strings, undefined, null
    let cleanValue: any = value
    if (Array.isArray(value)) {
      if (typeof value[0] === 'string') {
        const filtered = (value as string[]).filter(v => v && v !== '' && v !== undefined && v !== null)
        cleanValue = filtered.length === 0 ? undefined : filtered
      } else {
        const filtered = (value as number[]).filter(v => v !== undefined && v !== null)
        cleanValue = filtered.length === 0 ? undefined : filtered
      }
    } else if (!value || value === '' || value === 'undefined') {
      cleanValue = undefined
    } else if (key === 'difficulty_level' && typeof value === 'string') {
      // Convert difficulty level string to number
      cleanValue = parseInt(value) || undefined
    }

    const newFilters = { ...filters, [key]: cleanValue, page: 1 }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleSearch = useCallback((searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm, page: 1 }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handlePageChange = useCallback((page: number) => {
    const newFilters = { ...filters, page }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleItemsPerPageChange = useCallback((limit: number) => {
    const newFilters = { ...filters, limit, page: 1 }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleClearFilters = useCallback(() => {
    clearFilters()
    setSearchParams({})
  }, [clearFilters, setSearchParams])

  const handleContentClick = useCallback((content: ContentSet) => {
    // Navigate to task set detail page
    navigate(`/tasks/${content._id}`)
  }, [navigate])

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'page' || key === 'limit') return false
    if (Array.isArray(value)) return value.length > 0
    return value !== undefined && value !== ''
  })

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-4">
      {/* Search bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search content sets..."
            value={filters.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>

        {/* Active filters indicator and clear button */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {Object.entries(filters).filter(([key, value]) => {
                if (key === 'page' || key === 'limit') return false
                if (Array.isArray(value)) return value.length > 0
                return value !== undefined && value !== ''
              }).length} filters active
            </span>
            <button
              onClick={handleClearFilters}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Always visible filter options */}
      {!filtersLoading && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Theme filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Theme</label>
            <select
              value={filters.theme_id || ''}
              onChange={(e) => handleFilterChange('theme_id', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Themes</option>
              {filterGroups.find((g: any) => g.key === 'theme_id')?.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Status filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Status</label>
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Status</option>
              {filterGroups.find((g: any) => g.key === 'status')?.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Generation Type filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Type</label>
            <select
              value={filters.gentype || ''}
              onChange={(e) => handleFilterChange('gentype', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Types</option>
              {filterGroups.find((g: any) => g.key === 'gentype')?.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Difficulty</label>
            <select
              value={filters.difficulty_level || ''}
              onChange={(e) => handleFilterChange('difficulty_level', e.target.value)}
              className={cn(
                'w-full px-3 py-2 rounded-md border border-border',
                'bg-background text-foreground text-sm',
                'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                'transition-all duration-200'
              )}
            >
              <option value="">All Levels</option>
              <option value="1">Level 1 (Easy)</option>
              <option value="2">Level 2 (Medium)</option>
              <option value="3">Level 3 (Hard)</option>
            </select>
          </div>
        </div>
      )}
    </div>
  )

  // Bottom content (pagination)
  const bottomContent = !loading && !error && content.length > 0 ? (
    <PaginationControls
      currentPage={pagination.currentPage}
      totalPages={pagination.totalPages}
      totalItems={pagination.totalItems}
      itemsPerPage={pagination.itemsPerPage}
      onPageChange={handlePageChange}
      onItemsPerPageChange={handleItemsPerPageChange}
    />
  ) : null

  return (
    <MainLayout
      title="📚 Anthology"
      description="Discover and explore curated content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Content Area */}
      {loading ? (
        <LoadingGrid count={8} type="content" />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load content"
          description={error}
          actionLabel="Try again"
          onAction={refetch}
        />
      ) : content.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'content'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : refetch}
        />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {content.map((item: ContentSet) => (
            <ContentCard
              key={item._id}
              content={item}
              onClick={handleContentClick}
              showTheme={true}
            />
          ))}
        </div>
      )}
    </MainLayout>
  )
}

export default Anthology
