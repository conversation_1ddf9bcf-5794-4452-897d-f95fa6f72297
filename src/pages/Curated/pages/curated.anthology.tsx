import React, { useState, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Filter } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { 
  ContentCard, 
  SearchBar, 
  FilterPanel, 
  PaginationControls, 
  LoadingGrid, 
  EmptyState 
} from '../../../components/curated'
import { useCuratedContent, useContentFilters } from '../hooks'
import { ContentSet } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

/**
 * Anthology Page
 * Main curated content gallery with filtering and search
 * Displays all content sets with theme information
 */
const Anthology: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [showFilters, setShowFilters] = useState(false)

  // Get initial filters from URL params
  const getInitialFilters = () => {
    const filters: any = {}
    if (searchParams.get('theme_id')) filters.theme_id = searchParams.get('theme_id')
    if (searchParams.get('difficulty_level')) filters.difficulty_level = parseInt(searchParams.get('difficulty_level')!)
    if (searchParams.get('status')) filters.status = searchParams.get('status')
    if (searchParams.get('gentype')) filters.gentype = searchParams.get('gentype')
    if (searchParams.get('search')) filters.search = searchParams.get('search')
    if (searchParams.get('page')) filters.page = parseInt(searchParams.get('page')!)
    return filters
  }

  const {
    content,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refetch,
    clearFilters
  } = useCuratedContent(getInitialFilters())

  const {
    filterGroups,
    loading: filtersLoading
  } = useContentFilters()

  // Update URL when filters change
  const updateURL = useCallback((newFilters: any) => {
    const params = new URLSearchParams()
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v))
        } else {
          params.set(key, value.toString())
        }
      }
    })
    setSearchParams(params)
  }, [setSearchParams])

  const handleFilterChange = useCallback((key: string, value: string | string[] | number[]) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleSearch = useCallback((searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm, page: 1 }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handlePageChange = useCallback((page: number) => {
    const newFilters = { ...filters, page }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleItemsPerPageChange = useCallback((limit: number) => {
    const newFilters = { ...filters, limit, page: 1 }
    setFilters(newFilters)
    updateURL(newFilters)
  }, [filters, setFilters, updateURL])

  const handleClearFilters = useCallback(() => {
    clearFilters()
    setSearchParams({})
  }, [clearFilters, setSearchParams])

  const handleContentClick = useCallback((content: ContentSet) => {
    // Navigate to task set detail page
    navigate(`/tasks/${content._id}`)
  }, [navigate])

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'page' || key === 'limit') return false
    if (Array.isArray(value)) return value.length > 0
    return value !== undefined && value !== ''
  })

  return (
    <MainLayout
      title="📚 Anthology"
      description="Discover and explore curated content collections"
      className="space-y-6"
    >
      {/* Header with search and filter toggle */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search content sets..."
            value={filters.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={cn(
            'flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-200',
            showFilters
              ? 'bg-primary text-primary-foreground border-primary'
              : 'bg-background text-foreground border-border hover:border-primary/50'
          )}
        >
          <Filter className="w-4 h-4" />
          <span>Filters</span>
          {hasActiveFilters && (
            <span className="px-2 py-1 bg-primary/20 text-primary text-xs rounded-full">
              {Object.entries(filters).filter(([key, value]) => {
                if (key === 'page' || key === 'limit') return false
                if (Array.isArray(value)) return value.length > 0
                return value !== undefined && value !== ''
              }).length}
            </span>
          )}
        </button>
      </div>

      {/* Filter panel */}
      {showFilters && !filtersLoading && (
        <FilterPanel
          filters={filterGroups.map((group: any) => ({
            ...group,
            value: filters[group.key as keyof typeof filters]
          }))}
          onFilterChange={handleFilterChange}
          onClearAll={hasActiveFilters ? handleClearFilters : undefined}
          className="mb-6"
        />
      )}

      {/* Content grid */}
      <div className="space-y-6">
        {loading ? (
          <LoadingGrid count={8} type="content" />
        ) : error ? (
          <EmptyState
            type="error"
            title="Failed to load content"
            description={error}
            actionLabel="Try again"
            onAction={refetch}
          />
        ) : content.length === 0 ? (
          <EmptyState
            type={hasActiveFilters ? 'search' : 'content'}
            actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
            onAction={hasActiveFilters ? handleClearFilters : refetch}
          />
        ) : (
          <>
            {/* Content grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {content.map((item: ContentSet) => (
                <ContentCard
                  key={item._id}
                  content={item}
                  onClick={handleContentClick}
                  showTheme={true}
                />
              ))}
            </div>

            {/* Pagination */}
            <PaginationControls
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              totalItems={pagination.totalItems}
              itemsPerPage={pagination.itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              className="mt-8"
            />
          </>
        )}
      </div>
    </MainLayout>
  )
}

export default Anthology
